import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/helpers/color_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CardTentangSaya extends StatelessWidget {
  const CardTentangSaya({
    super.key,
    required this.timelineData,
    required this.infoCVController,
  });

  final List<Map<String, String>> timelineData;
  final GetInfoCVController infoCVController;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              // ✅ Ganti `Expanded` dengan `Flexible`
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min, // ✅ Gunakan `min`
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Material(
                      color: Colors.white,
                      shape: RoundedRectangleBorder(
                        side: BorderSide(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          children: [
                            Obx(
                              () => (infoCVController
                                      .tentangSaya.value.isNotEmpty
                                  ? Column(
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.person,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.primaryColor,
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Text(
                                                  "dcv.tentang_saya".tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                GestureDetector(
                                                  onTap: () async {
                                                    // var result =
                                                    //     await Get.toNamed(
                                                    //   Routes.pendidikan,
                                                    //   arguments: {
                                                    //     "pendidikan": null,
                                                    //     "diploma":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .diploma
                                                    //             : "",
                                                    //     "pendidikan_terakhir":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .pendidikanTerakhir
                                                    //             : "",
                                                    //   },
                                                    // );

                                                    // if (result == true) {
                                                    //   infoCVController.loadRH();
                                                    //   infoCVController
                                                    //       .loadRiwayatPendidikan();
                                                    // }
                                                  },
                                                  child: Icon(
                                                    Icons.edit,
                                                    size: 18,
                                                    color: ColorAsset
                                                        .secodaryColor,
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    )
                                  : Column(
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.person,
                                                  size: 18,
                                                  color:
                                                      ColorAsset.primaryColor,
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Text(
                                                  "dcv.tentang_saya".tr,
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                // Text(
                                                //   "tombol.tambah_data".tr,
                                                //   style: TextStyle(
                                                //     fontSize: 14,
                                                //     fontWeight: FontWeight.bold,
                                                //   ),
                                                // ),
                                                SizedBox(
                                                  width: 10,
                                                ),
                                                GestureDetector(
                                                  onTap: () async {
                                                    // var result =
                                                    //     await Get.toNamed(
                                                    //   Routes.pendidikan,
                                                    //   arguments: {
                                                    //     "pendidikan": null,
                                                    //     "diploma":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .diploma
                                                    //             : "",
                                                    //     "pendidikan_terakhir":
                                                    //         infoCVController
                                                    //                 .rhList
                                                    //                 .isNotEmpty
                                                    //             ? infoCVController
                                                    //                 .rhList[0]
                                                    //                 .pendidikanTerakhir
                                                    //             : "",
                                                    //   },
                                                    // );

                                                    // if (result == true) {
                                                    //   infoCVController.loadRH();
                                                    //   infoCVController
                                                    //       .loadRiwayatPendidikan();
                                                    // }
                                                  },
                                                  child: Icon(
                                                    Icons.add,
                                                    size: 18,
                                                    color: ColorAsset
                                                        .secodaryColor,
                                                  ),
                                                ),
                                              ],
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Divider(
                                          thickness: 1,
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                      ],
                                    )),
                            ),
                            Obx(() => Skeletonizer(
                                enabled:
                                    infoCVController.isLoadingTentangSaya.value,
                                child: (!infoCVController
                                        .tentangSaya.value.isEmpty
                                    ? Column(
                                        children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      infoCVController
                                                          .tentangSaya.value,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.normal,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons.person,
                                                      size: 18,
                                                      color: ColorAsset
                                                          .primaryColor,
                                                    ),
                                                    SizedBox(
                                                      width: 8,
                                                    ),
                                                    Text(
                                                      "dcv.tentang_saya".tr,
                                                      style: TextStyle(
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () async {
                                              // var result = await Get.toNamed(
                                              //   Routes.pendidikan,
                                              //   arguments: {
                                              //     "pendidikan": null,
                                              //     "diploma": infoCVController
                                              //             .rhList.isNotEmpty
                                              //         ? infoCVController
                                              //             .rhList[0].diploma
                                              //         : "",
                                              //     "pendidikan_terakhir":
                                              //         infoCVController
                                              //                 .rhList.isNotEmpty
                                              //             ? infoCVController
                                              //                 .rhList[0]
                                              //                 .pendidikanTerakhir
                                              //             : "",
                                              //   },
                                              // );

                                              // if (result == true) {
                                              //   infoCVController.loadRH();
                                              //   infoCVController
                                              //       .loadRiwayatPendidikan();
                                              // }
                                            },
                                            child: Icon(
                                              Icons.add,
                                              size: 18,
                                              color: ColorAsset.secodaryColor,
                                            ),
                                          ),
                                        ],
                                      )))),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
