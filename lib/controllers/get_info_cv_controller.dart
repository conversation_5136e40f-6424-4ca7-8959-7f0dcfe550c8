import 'package:digital_cv_mobile/components/snackbar_custom.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/models/kursus_model.dart';
import 'package:digital_cv_mobile/models/organisasi_model.dart';
import 'package:digital_cv_mobile/models/pekerjaan_model.dart';
import 'package:digital_cv_mobile/models/pendidikan_model.dart';
import 'package:digital_cv_mobile/models/penguasaan_bahasa_model.dart';
import 'package:digital_cv_mobile/models/rh_model.dart';
import 'package:digital_cv_mobile/services/form_service.dart';
import 'package:digital_cv_mobile/services/get_info_cv_service.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GetInfoCVController extends GetxController {
  final GetInfoCVService infoCVService = GetInfoCVService();
  final snackbar = Get.find<SnackBarService>();
  final prefs = Get.find<SharedPreferences>();
  String pin = "";
  final RxBool isLoadingRH = false.obs;
  final RxBool isLoadingPendidikan = false.obs;
  final RxBool isLoadingTentangSaya = false.obs;
  final RxBool isLoadingKursus = false.obs;
  final RxBool isLoadingPekerjaan = false.obs;
  final RxBool isLoadingOrganisasi = false.obs;
  final RxBool isLoadingBahasa = false.obs;
  final RxBool isLoadingCVAI = false.obs;
  final RxString analisaCVAI = ''.obs;

  RxList<RhModel> rhList = <RhModel>[].obs;
  RxList<PendidikanModel> pendidikanList = <PendidikanModel>[].obs;
  RxList<KursusModel> kursusList = <KursusModel>[].obs;
  RxList<PekerjaanModel> pekerjaanList = <PekerjaanModel>[].obs;
  RxList<OrganisasiModel> organisasiList = <OrganisasiModel>[].obs;
  RxList<PenguasaanBahasaModel> penguasaanBahasaList =
      <PenguasaanBahasaModel>[].obs;
  RxString tentangSaya = "".obs;

  RxDouble progress = 0.0.obs;

  // @override
  // void onInit() {
  //   super.onInit();
  //   loadRH();
  //   loadRiwayatPekerjaan();
  //   loadRiwayatPendidikan();
  //   loadRiwayatKursus();
  //   loadRiwayatOrganisasi();
  //   loadPenguasaanBahasa();
  // }

  void loadRH() async {
    rhList.clear();
    tentangSaya.value = "";
    isLoadingTentangSaya(true);
    List<RhModel> result = await getRH();

    LogService.log.d("Load RH : $result");
    if (result.isNotEmpty) {
      rhList.value = result;
      tentangSaya.value = result.first.tentangSaya ?? "";
    }
    isLoadingTentangSaya(false);
    hitungProgress();
  }

  void loadRiwayatPendidikan() async {
    pendidikanList.clear();
    List<PendidikanModel> result = await getRiwayatPendidikan();

    if (result.isNotEmpty) {
      pendidikanList.value = result;
    }
    hitungProgress();
  }

  void loadRiwayatKursus() async {
    kursusList.clear();
    List<KursusModel> result = await getRiwayatKursus();

    if (result.isNotEmpty) {
      kursusList.value = result;
    }
    hitungProgress();
  }

  void loadRiwayatPekerjaan() async {
    pekerjaanList.clear();
    List<PekerjaanModel> result = await getRiwayatPekerjaan();

    if (result.isNotEmpty) {
      pekerjaanList.value = result;
    }
    hitungProgress();
  }

  void loadRiwayatOrganisasi() async {
    organisasiList.clear();
    List<OrganisasiModel> result = await getRiwayatOrganisasi();

    if (result.isNotEmpty) {
      organisasiList.value = result;
    }
    hitungProgress();
  }

  void loadPenguasaanBahasa() async {
    penguasaanBahasaList.clear();
    List<PenguasaanBahasaModel> result = await getPenguasaanBahasa();

    if (result.isNotEmpty) {
      penguasaanBahasaList.value = result;
    }
    hitungProgress();
  }

  void hitungProgress() {
    int total = 6;
    int filled = 0;

    // if (rhList.isNotEmpty &&
    //     rhList.first.ktp != "" &&
    //     rhList.first.ktp.isNotEmpty) {
    //   filled++;
    // }

    if (pendidikanList.isNotEmpty) {
      filled++;
    }

    if (rhList.isNotEmpty &&
        rhList.first.pengalamanKerja != "" &&
        rhList.first.pengalamanKerja!.isNotEmpty) {
      filled++;
    }

    if (rhList.isNotEmpty &&
        rhList.first.kursus != "" &&
        rhList.first.kursus!.isNotEmpty) {
      filled++;
    }

    if (rhList.isNotEmpty &&
        rhList.first.minatGaji != "" &&
        rhList.first.minatGaji!.isNotEmpty) {
      filled++;
    }

    if (rhList.isNotEmpty &&
        rhList.first.kelebihan != "" &&
        rhList.first.kelebihan!.isNotEmpty) {
      filled++;
    }

    if (rhList.isNotEmpty &&
        rhList.first.organisasi != "" &&
        rhList.first.organisasi!.isNotEmpty) {
      filled++;
    }

    progress.value = filled / total;
  }

  Future<List<RhModel>> getRH() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingRH(true);
      var response = await infoCVService.getRH(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<RhModel> result =
                  dataList.map((item) => RhModel.fromJson(item)).toList();

              LogService.log.i("DATA LIST : $result");

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          LogService.log
              .e("${"controller.invalid_format".tr} GetInfoCVController");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingRH(false);
    }
  }

  Future<List<PendidikanModel>> getRiwayatPendidikan() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingPendidikan(true);
      var response = await infoCVService.getRiwayatPendidikan(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<PendidikanModel> result = dataList
                  .map((item) => PendidikanModel.fromJson(item))
                  .toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} GetInfoCVController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingPendidikan(false);
    }
  }

  Future<List<KursusModel>> getRiwayatKursus() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingKursus(true);
      var response = await infoCVService.getRiwayatKursus(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<KursusModel> result =
                  dataList.map((item) => KursusModel.fromJson(item)).toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} GetInfoCVController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingKursus(false);
    }
  }

  Future<List<PekerjaanModel>> getRiwayatPekerjaan() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingPekerjaan(true);
      var response = await infoCVService.getRiwayatPekerjaan(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<PekerjaanModel> result = dataList
                  .map((item) => PekerjaanModel.fromJson(item))
                  .toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} GetInfoCVController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingPekerjaan(false);
    }
  }

  Future<List<OrganisasiModel>> getRiwayatOrganisasi() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingOrganisasi(true);
      var response = await infoCVService.getRiwayatOrganisasi(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<OrganisasiModel> result = dataList
                  .map((item) => OrganisasiModel.fromJson(item))
                  .toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} GetInfoCVController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingOrganisasi(false);
    }
  }

  Future<List<PenguasaanBahasaModel>> getPenguasaanBahasa() async {
    try {
      pin = prefs.getString("pin") ?? "";
      isLoadingBahasa(true);
      var response = await infoCVService.getPenguasaanBahasa(pin);

      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          Map<String, dynamic> responseData = response.data;
          if (responseData["success"] == true) {
            if (responseData.containsKey("data") &&
                responseData["data"] is List) {
              List dataList = responseData["data"];
              final List<PenguasaanBahasaModel> result = dataList
                  .map((item) => PenguasaanBahasaModel.fromJson(item))
                  .toList();

              return result;
            } else {
              // // snackbar.showError("controller.data_not_found".tr);
              return [];
            }
          } else {
            snackbar.showError("controller.show_data_failed".tr);
            return [];
          }
        } else {
          snackbar.showError(
              "${"controller.invalid_format".tr} GetInfoCVController");
          LogService.log.e(
              "Expected Map<String, dynamic>, got ${response.data.runtimeType}");
          return [];
        }
      } else if (response.statusCode == 400) {
        String errorMessage = "controller.unauthorized".tr;
        snackbar.showError(errorMessage);
        return [];
      } else {
        LogService.log
            .e("${"controller.server_error".tr}: ${response.statusCode}");
        return [];
      }
    } catch (e) {
      LogService.log.e("Login error: $e");
      return [];
    } finally {
      isLoadingBahasa(false);
    }
  }

  Future<bool> generateCVAI() async {
    isLoadingCVAI.value = true;
    var response = await FormService().analisaCVAI();
    if (response.data['status'] == 'success') {
      analisaCVAI.value = response.data['data'];
      isLoadingCVAI.value = false;
      return true;
    } else {
      analisaCVAI.value = '';
      snackbar.showError("controller.show_data_failed".tr);
      isLoadingCVAI.value = false;
      return false;
    }
  }
}
